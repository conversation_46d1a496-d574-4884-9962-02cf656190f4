<template>
  <view class="camera-container">
    <camera
      device-position="back"
      :flash="flashMode"
      class="camera"
      @error="handleError"
      style="width: 100%; height: 100%;"
    ></camera>
    
    <!-- 顶部状态栏 -->
    <!-- <view class="status-bar">
      <view class="status-title">{{ isBackSide ? '身份证反面' : '身份证正面' }}</view>
    </view>
     -->
    <!-- 身份证取景框 - 使用图片作为覆盖层 -->
    <view class="id-card-overlay">
      <image 
        class="overlay-image" 
        :src="isBackSide ? '/static/images/camera_module_side.png' : '/static/images/camera_module_front.png'"
      ></image>
    </view>
    
    
    <!-- 拍照和取消按钮 -->
    <view class="camera-buttons">
      <view class="btn cancel-btn" @tap="handleCancel">取消</view>
      <view class="btn take-photo-btn" @tap="takePhoto">拍照</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ctx: null,
      isBackSide: false,
      flashMode: 'auto' // 使用自动闪光灯模式
    }
  },
  
  onLoad(options) {
    // 解析URL参数
    this.isBackSide = options.isBackSide === '1';
    console.log('相机模式:', this.isBackSide ? '身份证反面' : '身份证正面');
  },
  
  mounted() {
    // 创建相机上下文
    this.ctx = uni.createCameraContext();
  },
  
  methods: {
    takePhoto() {
      if (!this.ctx) {
        uni.showToast({
          title: '相机初始化失败',
          icon: 'none'
        });
        return;
      }
      
      // 播放相机快门声
      const innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.autoplay = true;
      innerAudioContext.src = '/static/audio/camera_shutter.mp3';
      
      // 拍照
      this.ctx.takePhoto({
        quality: 'high',
        success: (res) => {
          // 返回上一页并传递图片路径
          uni.navigateBack({
            delta: 1,
            success: () => {
              const eventChannel = this.getOpenerEventChannel();
              eventChannel.emit('photoTaken', {
                tempImagePath: res.tempImagePath
              });
            }
          });
        },
        fail: (err) => {
          console.error('拍照失败:', err);
          uni.showToast({
            title: '拍照失败',
            icon: 'none'
          });
        }
      });
    },
    
    handleCancel() {
      uni.navigateBack({ delta: 1 });
    },
    
    handleError(e) {
      console.error('相机错误:', e.detail);
      uni.showToast({
        title: '相机启动失败，请检查相机权限',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.camera-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999;
  background-color: #000;
}

.camera {
  width: 100%;
  height: 100%;
}

/* 顶部状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), transparent);
  z-index: 10;
}

.status-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 新增覆盖层样式 */
.id-card-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  height: 70%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.overlay-image {
  width: 84%;
  height: 84%;
  object-fit: contain;
}

.camera-tips {
  position: absolute;
  bottom: 170px;
  left: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 12px 0;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.camera-buttons {
  position: absolute;
  bottom: 60px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-around;
  padding: 0 40px;
  box-sizing: border-box;
}

.btn {
  width: 140px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0,0,0,0.4);
  transition: all 0.2s ease;
}

.take-photo-btn {
  background: linear-gradient(180deg, #4c8bf5 0%, #2b6de8 100%);
  color: #fff;
  border: none;
}

.cancel-btn {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.6);
}
</style> 