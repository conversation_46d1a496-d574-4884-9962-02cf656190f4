<template>
  <view class="container">
    <!-- 头部区域 -->
    <view class="header-section">
      <!-- 月份选择 -->
      <view class="month-header">
        <view class="month-nav" @click="prevMonth">
          <uni-icons type="left" size="18" color="#666"></uni-icons>
        </view>
        <text class="month-title">{{ currentMonthText }}</text>
        <view class="month-nav" @click="nextMonth">
          <uni-icons type="right" size="18" color="#666"></uni-icons>
        </view>
      </view>

      <!-- 简化统计 -->
      <view class="stats-row">
        <text class="stats-text">本月工作 {{ monthStats.workDays }} 天，共 {{ monthStats.totalHours }}</text>
      </view>
    </view>

    <!-- 简约日历 -->
    <view class="calendar">
      <view class="week-header">
        <text class="week-day" v-for="day in weekDays" :key="day">{{ day }}</text>
      </view>

      <view class="calendar-grid">
        <view
          class="day-cell"
          v-for="day in calendarDays"
          :key="day.key"
          :class="{
            'other-month': !day.isCurrentMonth,
            'today': day.isToday,
            'has-record': day.hasRecord,
            'selected': day.isSelected
          }"
          @click="selectDay(day)"
        >
          <text class="day-text">{{ day.day }}</text>
          <view class="dot" v-if="day.hasRecord"></view>
        </view>
      </view>
    </view>

    <!-- 记录详情 -->
    <view class="records-section" v-if="selectedDay">
      <view class="date-header">
        <text class="date-title">{{ selectedDateText }}</text>
        <text class="work-summary" v-if="dayStats.workTime">{{ dayStats.workTime }}</text>
      </view>

      <view class="record-list" v-if="selectedDayRecords.length > 0">
        <view
          class="record-item"
          v-for="record in selectedDayRecords"
          :key="record.id"
        >
          <view class="record-time">{{ formatTime(record.time) }}</view>
          <view class="record-info">
            <text class="record-type" :class="record.type">{{ getRecordTypeText(record.type) }}</text>
            <text class="record-location">{{ record.location }}</text>
          </view>
        </view>
      </view>

      <view class="empty-state" v-else>
        <text class="empty-text">暂无打卡记录</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDate: new Date(),
      selectedDay: null,
      checkinRecords: [],
      weekDays: ['日', '一', '二', '三', '四', '五', '六']
    }
  },
  
  computed: {
    currentMonthText() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth() + 1
      return `${year}年${month}月`
    },
    
    selectedDateText() {
      if (!this.selectedDay) return ''
      const year = this.selectedDay.year
      const month = this.selectedDay.month + 1
      const day = this.selectedDay.day
      return `${year}年${month}月${day}日`
    },
    
    calendarDays() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()
      
      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      
      // 获取第一天是星期几
      const firstDayWeek = firstDay.getDay()
      
      const days = []
      
      // 添加上个月的日期
      const prevMonth = new Date(year, month - 1, 0)
      for (let i = firstDayWeek - 1; i >= 0; i--) {
        const day = prevMonth.getDate() - i
        days.push({
          key: `prev-${day}`,
          day,
          year: prevMonth.getFullYear(),
          month: prevMonth.getMonth(),
          isCurrentMonth: false,
          isToday: false,
          hasRecord: false,
          isSelected: false
        })
      }
      
      // 添加当月的日期
      for (let day = 1; day <= lastDay.getDate(); day++) {
        const date = new Date(year, month, day)
        const isToday = this.isToday(date)
        const hasRecord = this.hasRecordOnDate(date)
        const isSelected = this.selectedDay && 
          this.selectedDay.year === year && 
          this.selectedDay.month === month && 
          this.selectedDay.day === day
        
        days.push({
          key: `current-${day}`,
          day,
          year,
          month,
          isCurrentMonth: true,
          isToday,
          hasRecord,
          isSelected
        })
      }
      
      // 添加下个月的日期，补齐6行
      const remainingDays = 42 - days.length
      const nextMonth = new Date(year, month + 1, 1)
      for (let day = 1; day <= remainingDays; day++) {
        days.push({
          key: `next-${day}`,
          day,
          year: nextMonth.getFullYear(),
          month: nextMonth.getMonth(),
          isCurrentMonth: false,
          isToday: false,
          hasRecord: false,
          isSelected: false
        })
      }
      
      return days
    },
    
    selectedDayRecords() {
      if (!this.selectedDay) return []

      const selectedDate = new Date(
        this.selectedDay.year,
        this.selectedDay.month,
        this.selectedDay.day
      )

      return this.checkinRecords.filter(record => {
        const recordDate = new Date(record.time)
        return recordDate.toDateString() === selectedDate.toDateString()
      }).sort((a, b) => new Date(a.time) - new Date(b.time))
    },

    // 当日统计
    dayStats() {
      if (!this.selectedDayRecords.length) return {}

      const checkinRecord = this.selectedDayRecords.find(r => r.type === 'checkin')
      const checkoutRecord = this.selectedDayRecords.find(r => r.type === 'checkout')

      const stats = {}

      if (checkinRecord) {
        stats.checkinTime = this.formatTime(checkinRecord.time)
      }

      if (checkoutRecord) {
        stats.checkoutTime = this.formatTime(checkoutRecord.time)
      }

      if (checkinRecord && checkoutRecord) {
        const workMinutes = (new Date(checkoutRecord.time) - new Date(checkinRecord.time)) / (1000 * 60)
        const hours = Math.floor(workMinutes / 60)
        const minutes = Math.floor(workMinutes % 60)
        stats.workTime = `${hours}小时${minutes}分钟`
      }

      return stats
    },

    // 当月统计
    monthStats() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()

      // 获取当月的所有记录
      const monthRecords = this.checkinRecords.filter(record => {
        const recordDate = new Date(record.time)
        return recordDate.getFullYear() === year && recordDate.getMonth() === month
      })

      // 按日期分组
      const recordsByDate = {}
      monthRecords.forEach(record => {
        const dateKey = new Date(record.time).toDateString()
        if (!recordsByDate[dateKey]) {
          recordsByDate[dateKey] = []
        }
        recordsByDate[dateKey].push(record)
      })

      let workDays = 0
      let totalMinutes = 0

      Object.values(recordsByDate).forEach(dayRecords => {
        const checkin = dayRecords.find(r => r.type === 'checkin')
        const checkout = dayRecords.find(r => r.type === 'checkout')

        if (checkin && checkout) {
          workDays++
          const workMinutes = (new Date(checkout.time) - new Date(checkin.time)) / (1000 * 60)
          totalMinutes += workMinutes
        }
      })

      const totalHours = Math.floor(totalMinutes / 60)
      const avgHours = workDays > 0 ? (totalMinutes / workDays / 60).toFixed(1) : '0.0'

      return {
        workDays,
        totalHours: `${totalHours}h`,
        avgHours: `${avgHours}h`
      }
    }
  },
  
  onLoad() {
    this.loadCheckinRecords()
    this.selectToday()
  },
  
  methods: {
    prevMonth() {
      const newDate = new Date(this.currentDate)
      newDate.setMonth(newDate.getMonth() - 1)
      this.currentDate = newDate
      this.selectedDay = null

      // 如果切换到当前月份，自动选择今天
      const today = new Date()
      if (newDate.getFullYear() === today.getFullYear() && newDate.getMonth() === today.getMonth()) {
        this.$nextTick(() => {
          this.selectToday()
        })
      }
    },

    nextMonth() {
      const newDate = new Date(this.currentDate)
      newDate.setMonth(newDate.getMonth() + 1)
      this.currentDate = newDate
      this.selectedDay = null

      // 如果切换到当前月份，自动选择今天
      const today = new Date()
      if (newDate.getFullYear() === today.getFullYear() && newDate.getMonth() === today.getMonth()) {
        this.$nextTick(() => {
          this.selectToday()
        })
      }
    },
    
    selectDay(day) {
      if (!day.isCurrentMonth) return
      this.selectedDay = day
    },

    selectToday() {
      const today = new Date()
      const year = today.getFullYear()
      const month = today.getMonth()
      const day = today.getDate()

      // 如果当前显示的月份是今天所在的月份，则选择今天
      if (this.currentDate.getFullYear() === year && this.currentDate.getMonth() === month) {
        this.selectedDay = {
          year,
          month,
          day,
          isCurrentMonth: true,
          isToday: true,
          hasRecord: this.hasRecordOnDate(today),
          isSelected: true
        }
      }
    },
    
    isToday(date) {
      const today = new Date()
      return date.toDateString() === today.toDateString()
    },
    
    hasRecordOnDate(date) {
      return this.checkinRecords.some(record => {
        const recordDate = new Date(record.time)
        return recordDate.toDateString() === date.toDateString()
      })
    },
    
    loadCheckinRecords() {
      // 从缓存加载打卡记录
      const records = uni.getStorageSync('checkinRecords')
      if (records && Array.isArray(records)) {
        this.checkinRecords = records.map(record => ({
          ...record,
          time: new Date(record.time)
        }))
      }
    },
    
    getRecordTypeText(type) {
      return type === 'checkin' ? '上线打卡' : '下线打卡'
    },
    
    formatTime(time) {
      const hours = time.getHours().toString().padStart(2, '0')
      const minutes = time.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f8f9fa;
}

// 头部区域
.header-section {
  background: white;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.month-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.month-nav {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.month-nav:active {
  background: #e0e0e0;
}

.month-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-row {
  text-align: center;
}

.stats-text {
  font-size: 26rpx;
  color: #666;
}

// 日历样式
.calendar {
  background: white;
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.week-header {
  display: flex;
  margin-bottom: 16rpx;
}

.week-day {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #999;
  font-weight: 500;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
}

.day-cell {
  width: 14.28%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 8rpx;
}

.day-cell.other-month .day-text {
  color: #d9d9d9;
}

.day-cell.today .day-text {
  background: #4c6ef5;
  color: white;
  font-weight: 600;
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-cell.selected .day-text {
  background: #722ed1;
  color: white;
  font-weight: 600;
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-cell.has-record:not(.today):not(.selected) .day-text {
  background: #f0f8ff;
  color: #4c6ef5;
  font-weight: 500;
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-text {
  font-size: 28rpx;
  color: #333;
}

.dot {
  width: 6rpx;
  height: 6rpx;
  background: #4c6ef5;
  border-radius: 50%;
  position: absolute;
  bottom: 12rpx;
}

.day-cell.today .dot,
.day-cell.selected .dot,
.day-cell.has-record .dot {
  display: none;
}

// 记录区域样式
.records-section {
  background: white;
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.work-summary {
  font-size: 24rpx;
  color: #4c6ef5;
  font-weight: 500;
}

.record-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f9fa;

  &:last-child {
    border-bottom: none;
  }
}

.record-time {
  width: 120rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 24rpx;
}

.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.record-type {
  font-size: 26rpx;
  font-weight: 500;

  &.checkin {
    color: #52c41a;
  }

  &.checkout {
    color: #ff4d4f;
  }
}

.record-location {
  font-size: 24rpx;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}


</style>
